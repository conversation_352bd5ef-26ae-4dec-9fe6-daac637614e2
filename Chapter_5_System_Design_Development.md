# Chapter 5: System Design and Development

## 5.1 Introduction

This chapter presents the detailed design and development process of the Palestine Tickets system, a comprehensive web-based platform developed by three computer science students. The system was designed to address the growing need for efficient event ticketing and integrated transportation services in Palestine, combining modern web technologies with user-centric design principles.

## 5.2 System Architecture

### 5.2.1 Overall Architecture Design

The Palestine Tickets system follows a three-tier architecture pattern, which was chosen for its simplicity and effectiveness for a student project of this scope. The architecture consists of:

**Presentation Layer (Frontend)**
- User interface built with HTML5, CSS3, and JavaScript
- Responsive design using Tailwind CSS framework
- Arabic language support with RTL (Right-to-Left) layout
- FontAwesome icons for enhanced user experience

**Business Logic Layer (Backend)**
- PHP 8.0+ with Object-Oriented Programming principles
- MVC (Model-View-Controller) pattern implementation
- Custom authentication and authorization system
- Integrated notification system

**Data Layer (Database)**
- MySQL 8.0 database management system
- Database name: `tickets_db`
- Normalized database design with proper relationships
- Comprehensive logging and audit trails

### 5.2.2 Technology Stack Selection

The technology stack was carefully selected based on the team's expertise and project requirements:

- **Backend**: PHP was chosen for its ease of learning, extensive documentation, and strong community support
- **Database**: MySQL was selected for its reliability and the team's familiarity with relational databases
- **Frontend**: Tailwind CSS was chosen for rapid UI development and responsive design capabilities
- **Development Environment**: XAMPP was used for local development, providing an integrated Apache, MySQL, and PHP environment

### 5.2.3 Project Structure

```
new1/ (Root Directory)
├── admin/                  # Administrative dashboard
├── assets/                 # Static files (CSS, JS, images)
├── config/                 # Configuration files
├── includes/               # Shared PHP files and functions
├── lang/                   # Language files (Arabic/English)
├── transport/              # Integrated transport system
├── uploads/                # User uploaded files
├── logs/                   # System logs
└── photo/                  # Event images
```

## 5.3 Database Design

### 5.3.1 Database Schema Overview

The database design follows normalization principles to ensure data integrity and minimize redundancy. The schema consists of 12 main tables that handle different aspects of the system:

### 5.3.2 Core Tables

**Users Table**
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(255) UNIQUE NOT NULL,
    user_password VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    role ENUM('user', 'transport_admin', 'notifications_admin', 'site_admin', 'super_admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Events Table**
```sql
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(255) NOT NULL,
    date_time DATETIME NOT NULL,
    end_time DATETIME,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    capacity INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(100),
    organizer VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    image VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Orders Table**
```sql
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    order_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    order_code VARCHAR(20) UNIQUE,
    payment_details JSON,
    technical_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

**Tickets Table**
```sql
CREATE TABLE tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(50) NOT NULL,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    ticket_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'used') DEFAULT 'pending',
    qr_code VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 5.3.3 Transport System Tables

**Transport Types Table**
```sql
CREATE TABLE transport_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'bus',
    capacity_min INT DEFAULT 1,
    capacity_max INT DEFAULT 50,
    price_per_km DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Starting Points Table**
```sql
CREATE TABLE starting_points (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**Transport Trips Table**
```sql
CREATE TABLE transport_trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    starting_point_id INT NOT NULL,
    transport_type_id INT NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME,
    price DECIMAL(10,2) NOT NULL,
    available_seats INT NOT NULL,
    total_seats INT NOT NULL,
    driver_name VARCHAR(255),
    driver_phone VARCHAR(20),
    vehicle_info TEXT,
    route_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id),
    FOREIGN KEY (starting_point_id) REFERENCES starting_points(id),
    FOREIGN KEY (transport_type_id) REFERENCES transport_types(id)
);
```

**Transport Bookings Table**
```sql
CREATE TABLE transport_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    trip_id INT NOT NULL,
    event_id INT NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    seats_count INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    special_notes TEXT,
    payment_method ENUM('bank_transfer', 'cash_on_delivery', 'mobile_pay', 'credit_card') NOT NULL,
    payment_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_details JSON,
    booking_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    has_event_ticket ENUM('yes', 'no') DEFAULT 'yes',
    ticket_amount DECIMAL(10,2) DEFAULT 0.00,
    transport_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (trip_id) REFERENCES transport_trips(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

### 5.3.4 Notification System Tables

**Notifications Table**
```sql
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    type ENUM('success','info','warning','danger','booking','payment','admin','transport','event','reminder') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**Notification Settings Table**
```sql
CREATE TABLE notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    mobile_enabled BOOLEAN DEFAULT FALSE,
    upcoming_tickets BOOLEAN DEFAULT TRUE,
    event_changes BOOLEAN DEFAULT TRUE,
    transport_updates BOOLEAN DEFAULT TRUE,
    payment_notifications BOOLEAN DEFAULT TRUE,
    admin_announcements BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 5.3.5 Security and Audit Tables

**Admin Permissions Table**
```sql
CREATE TABLE admin_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_type ENUM('transport', 'notifications', 'site', 'super') NOT NULL,
    granted_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_permission (user_id, permission_type),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (granted_by) REFERENCES users(id)
);
```

**Login Logs Table**
```sql
CREATE TABLE login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    user_agent TEXT NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    device VARCHAR(100) NOT NULL,
    login_time DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**Activity Logs Table**
```sql
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    description TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    browser VARCHAR(100),
    os VARCHAR(100),
    device VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 5.4 System Features Implementation

### 5.4.1 User Management System

The user management system was designed to handle different types of users with varying levels of access. The implementation includes:

**User Registration and Authentication**
- Secure password hashing using PHP's `password_hash()` function
- Email validation and verification system
- Session management with security measures
- Login attempt tracking with IP and device information

**Role-Based Access Control**
The system implements a five-tier role system:
1. **User**: Regular customers who can book tickets and transportation
2. **Transport Admin**: Manages transportation services and bookings
3. **Notifications Admin**: Handles system notifications and announcements
4. **Site Admin**: Manages events, users, and general site operations
5. **Super Admin**: Has full system access and can grant permissions to others

**Profile Management**
- User profile editing with image upload capability
- Security settings management
- Notification preferences configuration
- Account activity monitoring

### 5.4.2 Event Management System

The event management system allows administrators to create, manage, and monitor events effectively:

**Event Creation and Management**
- Comprehensive event details including title, description, location, and timing
- Image upload functionality for event promotion
- Capacity management with real-time ticket availability tracking
- Pricing system with support for original and discounted prices
- Event categorization and featured event highlighting

**Event Status Management**
- Draft mode for event preparation
- Publishing system for making events live
- Event cancellation with automatic notification system
- Archive functionality for completed events

### 5.4.3 Integrated Transportation System

One of the unique features of the Palestine Tickets system is its integrated transportation service:

**Transportation Planning**
- Multiple starting points for each event
- Various transportation types (buses, cars, vans)
- Flexible scheduling with departure and arrival times
- Driver and vehicle information management

**Booking Integration**
- Seamless integration with event ticket booking
- Combined payment processing for tickets and transportation
- Seat availability tracking and management
- Special notes and requirements handling

**Payment Processing**
- Multiple payment methods support
- Secure payment data handling
- Automatic booking confirmation
- Invoice generation for combined services

### 5.4.4 Comprehensive Notification System

The notification system keeps users informed about important events and updates:

**Notification Types**
- Success notifications for completed actions
- Warning notifications for important updates
- Booking confirmations and updates
- Payment status notifications
- Administrative announcements
- Event reminders and changes
- Transportation updates

**User Preferences**
- Customizable notification settings
- Email and SMS notification options
- Category-specific notification controls
- Real-time notification display
- Notification history and management

### 5.4.5 Administrative Dashboard

The administrative dashboard provides comprehensive system management capabilities:

**Dashboard Overview**
- Real-time statistics and metrics
- Sales reports with interactive charts
- User activity monitoring
- System health indicators

**Management Modules**
- Event management with full CRUD operations
- User management with role assignment
- Ticket management and status tracking
- Transportation service management
- Payment and financial reporting

## 5.5 Security Implementation

### 5.5.1 Data Security

Security was a primary concern throughout the development process:

**Password Security**
- Strong password hashing using bcrypt algorithm
- Password strength requirements
- Secure password reset functionality
- Session security with regeneration

**Input Validation and Sanitization**
- Comprehensive input validation for all user inputs
- SQL injection prevention using prepared statements
- XSS (Cross-Site Scripting) protection
- CSRF (Cross-Site Request Forgery) token implementation

**Data Encryption**
- Sensitive payment information encryption
- Secure data transmission protocols
- Database connection security
- File upload security measures

### 5.5.2 Access Control

**Authentication System**
- Secure login process with attempt limiting
- Session management with timeout
- Multi-level authorization system
- Activity logging and monitoring

**Permission Management**
- Granular permission system
- Role-based access control
- Permission inheritance and delegation
- Audit trail for permission changes

### 5.5.3 System Monitoring

**Logging System**
- Comprehensive activity logging
- Login attempt tracking
- Error logging and monitoring
- Performance metrics collection

**Security Monitoring**
- Suspicious activity detection
- Failed login attempt monitoring
- Unauthorized access prevention
- Regular security audit capabilities

## 5.6 Development Methodology

### 5.6.1 Development Approach

The team adopted an iterative development approach suitable for a student project:

**Phase 1: Planning and Design**
- Requirements gathering and analysis
- System architecture design
- Database schema design
- User interface mockups

**Phase 2: Core Development**
- Basic user authentication system
- Event management functionality
- Database implementation
- Basic user interface development

**Phase 3: Feature Enhancement**
- Transportation system integration
- Notification system implementation
- Administrative dashboard development
- Security feature implementation

**Phase 4: Testing and Refinement**
- Functionality testing
- Security testing
- User interface improvements
- Performance optimization

### 5.6.2 Team Collaboration

**Role Distribution**
- Student 1: Backend development and database design
- Student 2: Frontend development and user interface design
- Student 3: System integration and testing

**Development Tools**
- Version control using Git
- Local development environment with XAMPP
- Code editor with PHP and web development support
- Database management tools for MySQL

### 5.6.3 Quality Assurance

**Code Quality**
- Consistent coding standards
- Code documentation and comments
- Regular code reviews among team members
- Error handling and exception management

**Testing Procedures**
- Unit testing for critical functions
- Integration testing for system components
- User acceptance testing with sample users
- Security testing for vulnerabilities

## 5.7 Challenges and Solutions

### 5.7.1 Technical Challenges

**Database Design Complexity**
- Challenge: Designing a normalized database schema that supports complex relationships
- Solution: Iterative design process with regular reviews and adjustments

**Transportation Integration**
- Challenge: Seamlessly integrating transportation booking with event ticketing
- Solution: Unified booking process with shared session data and combined payment processing

**Real-time Updates**
- Challenge: Keeping ticket availability and transportation seats updated in real-time
- Solution: Database triggers and careful transaction management

### 5.7.2 Development Challenges

**Team Coordination**
- Challenge: Coordinating work among three team members with different schedules
- Solution: Regular meetings, clear task division, and version control system

**Learning Curve**
- Challenge: Learning new technologies and frameworks during development
- Solution: Dedicated learning time, online resources, and peer support

**Time Management**
- Challenge: Balancing project development with academic coursework
- Solution: Realistic timeline planning and priority management

### 5.7.3 User Experience Challenges

**Arabic Language Support**
- Challenge: Implementing proper RTL (Right-to-Left) layout and Arabic text support
- Solution: CSS direction properties and careful layout design

**Mobile Responsiveness**
- Challenge: Ensuring the system works well on various device sizes
- Solution: Responsive design using Tailwind CSS framework

**User Interface Consistency**
- Challenge: Maintaining consistent design across different system modules
- Solution: Shared CSS components and design system documentation

## 5.8 Chapter Summary

This chapter presented the comprehensive design and development process of the Palestine Tickets system. The system successfully integrates event ticketing with transportation services, providing a unique solution for event management in Palestine. The three-tier architecture, combined with modern web technologies and security best practices, resulted in a robust and user-friendly platform.

The development process demonstrated the team's ability to work collaboratively on a complex project, overcoming technical and logistical challenges while maintaining high standards for code quality and user experience. The implemented features, including the role-based access control, integrated transportation system, and comprehensive notification system, showcase the system's capability to handle real-world event management scenarios.

The next chapter will detail the system implementation and testing procedures, including deployment strategies and performance evaluation.
