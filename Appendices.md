# Appendices

## Appendix A: Database Schema Documentation

### A.1 Complete Database Schema

**Database Name:** `tickets_db`
**Character Set:** `utf8mb4`
**Collation:** `utf8mb4_unicode_ci`

### A.1.1 Core System Tables

**Table: users**
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(255) UNIQUE NOT NULL,
    user_password VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    role ENUM('user', 'transport_admin', 'notifications_admin', 'site_admin', 'super_admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (user_email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);
```

**Table: events**
```sql
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(255) NOT NULL,
    date_time DATETIME NOT NULL,
    end_time DATETIME,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    capacity INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(100),
    organizer VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    image VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_date_time (date_time),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_category (category)
);
```

**Table: orders**
```sql
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    order_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    order_code VARCHAR(20) UNIQUE,
    payment_details JSON,
    technical_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_event_id (event_id),
    INDEX idx_order_code (order_code),
    INDEX idx_payment_status (payment_status)
);
```

**Table: tickets**
```sql
CREATE TABLE tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(50) NOT NULL,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    ticket_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'used') DEFAULT 'pending',
    qr_code VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_ticket_code (ticket_code),
    INDEX idx_user_id (user_id),
    INDEX idx_event_id (event_id),
    INDEX idx_status (status)
);
```

### A.1.2 Transportation System Tables

**Table: transport_types**
```sql
CREATE TABLE transport_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'bus',
    capacity_min INT DEFAULT 1,
    capacity_max INT DEFAULT 50,
    price_per_km DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
);
```

**Table: starting_points**
```sql
CREATE TABLE starting_points (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_active (is_active)
);
```

**Table: transport_trips**
```sql
CREATE TABLE transport_trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    starting_point_id INT NOT NULL,
    transport_type_id INT NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME,
    price DECIMAL(10,2) NOT NULL,
    available_seats INT NOT NULL,
    total_seats INT NOT NULL,
    driver_name VARCHAR(255),
    driver_phone VARCHAR(20),
    vehicle_info TEXT,
    route_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (starting_point_id) REFERENCES starting_points(id) ON DELETE CASCADE,
    FOREIGN KEY (transport_type_id) REFERENCES transport_types(id) ON DELETE CASCADE,
    INDEX idx_event_id (event_id),
    INDEX idx_starting_point (starting_point_id),
    INDEX idx_departure_time (departure_time),
    INDEX idx_active (is_active)
);
```

**Table: transport_bookings**
```sql
CREATE TABLE transport_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    trip_id INT NOT NULL,
    event_id INT NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    seats_count INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    special_notes TEXT,
    payment_method ENUM('bank_transfer', 'cash_on_delivery', 'mobile_pay', 'credit_card') NOT NULL,
    payment_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_details JSON,
    booking_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    has_event_ticket ENUM('yes', 'no') DEFAULT 'yes',
    ticket_amount DECIMAL(10,2) DEFAULT 0.00,
    transport_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (trip_id) REFERENCES transport_trips(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_trip_id (trip_id),
    INDEX idx_booking_code (booking_code),
    INDEX idx_status (status)
);
```

### A.1.3 Notification System Tables

**Table: notifications**
```sql
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    type ENUM('success','info','warning','danger','booking','payment','admin','transport','event','reminder') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
);
```

**Table: notification_settings**
```sql
CREATE TABLE notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    mobile_enabled BOOLEAN DEFAULT FALSE,
    upcoming_tickets BOOLEAN DEFAULT TRUE,
    event_changes BOOLEAN DEFAULT TRUE,
    transport_updates BOOLEAN DEFAULT TRUE,
    payment_notifications BOOLEAN DEFAULT TRUE,
    admin_announcements BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id)
);
```

### A.1.4 Security and Audit Tables

**Table: admin_permissions**
```sql
CREATE TABLE admin_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_type ENUM('transport', 'notifications', 'site', 'super') NOT NULL,
    granted_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_permission (user_id, permission_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type)
);
```

**Table: login_logs**
```sql
CREATE TABLE login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    user_agent TEXT NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    device VARCHAR(100) NOT NULL,
    login_time DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_login_time (login_time),
    INDEX idx_ip_address (ip_address)
);
```

**Table: activity_logs**
```sql
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    description TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    browser VARCHAR(100),
    os VARCHAR(100),
    device VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
```

**Table: registration_logs**
```sql
CREATE TABLE registration_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    user_agent TEXT NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    device VARCHAR(100) NOT NULL,
    registration_time DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_registration_time (registration_time)
);
```

## Appendix B: API Documentation

### B.1 Authentication Endpoints

**POST /api/auth/login**
```json
Request:
{
    "email": "<EMAIL>",
    "password": "password123"
}

Response:
{
    "success": true,
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "role": "user"
    },
    "session_token": "abc123..."
}
```

**POST /api/auth/register**
```json
Request:
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "phone": "**********"
}

Response:
{
    "success": true,
    "user_id": 1,
    "message": "Registration successful"
}
```

### B.2 Event Management Endpoints

**GET /api/events**
```json
Response:
{
    "success": true,
    "events": [
        {
            "id": 1,
            "title": "Tech Conference 2024",
            "location": "Ramallah",
            "date_time": "2024-12-31 18:00:00",
            "price": 50.00,
            "original_price": 75.00,
            "available_tickets": 95,
            "is_featured": true
        }
    ],
    "total": 1,
    "page": 1,
    "per_page": 20
}
```

**POST /api/events**
```json
Request:
{
    "title": "New Event",
    "description": "Event description",
    "location": "Gaza",
    "date_time": "2024-12-31 18:00:00",
    "price": 25.00,
    "capacity": 100
}

Response:
{
    "success": true,
    "event_id": 2,
    "message": "Event created successfully"
}
```

### B.3 Booking Endpoints

**POST /api/bookings/event**
```json
Request:
{
    "event_id": 1,
    "quantity": 2,
    "payment_method": "credit_card"
}

Response:
{
    "success": true,
    "order_id": "ORD_123456",
    "total_amount": 100.00,
    "tickets": [
        {
            "ticket_code": "TKT_001",
            "qr_code": "data:image/png;base64,..."
        }
    ]
}
```

**POST /api/bookings/transport**
```json
Request:
{
    "trip_id": 1,
    "seats_count": 2,
    "customer_name": "John Doe",
    "customer_phone": "**********",
    "payment_method": "bank_transfer",
    "has_event_ticket": "yes"
}

Response:
{
    "success": true,
    "booking_code": "TRP_123456",
    "total_amount": 40.00,
    "departure_time": "16:00:00"
}
```

### B.4 Notification Endpoints

**GET /api/notifications**
```json
Response:
{
    "success": true,
    "notifications": [
        {
            "id": 1,
            "title": "Booking Confirmed",
            "message": "Your booking has been confirmed",
            "type": "success",
            "is_read": false,
            "created_at": "2024-01-15 10:30:00"
        }
    ],
    "unread_count": 5
}
```

**PUT /api/notifications/{id}/read**
```json
Response:
{
    "success": true,
    "message": "Notification marked as read"
}
```

## Appendix C: User Interface Screenshots

### C.1 Main Pages

**Homepage**
- Clean, modern design with featured events
- Arabic language support with RTL layout
- Responsive navigation menu
- Event categories and search functionality

**Event Details Page**
- Comprehensive event information
- Integrated transport booking option
- Real-time ticket availability
- Secure booking process

**Transport Booking Page**
- Starting point selection
- Trip options with pricing
- Seat availability display
- Combined payment processing

### C.2 Administrative Interface

**Admin Dashboard**
- Real-time statistics and metrics
- Interactive charts and graphs
- Quick access to management functions
- System health indicators

**Event Management**
- Event creation and editing forms
- Bulk operations for multiple events
- Image upload and management
- Status and availability controls

**User Management**
- User listing with search and filters
- Role assignment interface
- Activity monitoring
- Permission management

### C.3 Mobile Interface

**Responsive Design**
- Optimized for mobile devices
- Touch-friendly interface elements
- Simplified navigation for small screens
- Full functionality on mobile

**Mobile-Specific Features**
- Swipe gestures for navigation
- Mobile-optimized forms
- Quick access buttons
- Offline capability for tickets

## Appendix D: Installation and Setup Guide

### D.1 System Requirements

**Server Requirements:**
- PHP 8.0 or higher
- MySQL 8.0 or higher
- Apache 2.4 or Nginx 1.18+
- Minimum 2GB RAM
- 10GB available disk space

**Development Environment:**
- XAMPP 8.0.28 or higher
- Visual Studio Code (recommended)
- Git for version control
- Modern web browser (Chrome, Firefox, Safari, Edge)

### D.2 Installation Steps

**Step 1: Download and Extract**
```bash
# Clone the repository
git clone https://github.com/username/palestine-tickets.git

# Navigate to project directory
cd palestine-tickets

# Copy to web server directory
cp -r . /var/www/html/palestine-tickets/
```

**Step 2: Database Setup**
```sql
-- Create database
CREATE DATABASE tickets_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
mysql -u root -p tickets_db < database/schema.sql

-- Import sample data (optional)
mysql -u root -p tickets_db < database/sample_data.sql
```

**Step 3: Configuration**
```php
// config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'tickets_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');
```

**Step 4: File Permissions**
```bash
# Set proper permissions
chmod 755 uploads/
chmod 755 logs/
chmod 755 photo/
chmod 644 config/database.php
```

**Step 5: Create Admin User**
```sql
-- Create super admin user
INSERT INTO users (user_name, user_email, user_password, role, status) 
VALUES ('Admin', '<EMAIL>', 
        '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
        'super_admin', 'active');
```

### D.3 Configuration Options

**Security Settings**
```php
// Enable HTTPS in production
define('FORCE_HTTPS', true);

// Set session security
ini_set('session.cookie_secure', 1);
ini_set('session.cookie_httponly', 1);

// Configure error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
```

**Performance Settings**
```php
// Enable caching
define('ENABLE_CACHE', true);
define('CACHE_DURATION', 3600);

// Database connection pooling
define('DB_PERSISTENT', true);

// Image optimization
define('MAX_IMAGE_SIZE', 2097152); // 2MB
define('IMAGE_QUALITY', 85);
```

## Appendix E: Testing Documentation

### E.1 Test Cases

**User Registration Test Cases**
```
TC001: Valid Registration
- Input: Valid user data
- Expected: User created successfully
- Status: PASS

TC002: Duplicate Email
- Input: Existing email address
- Expected: Registration fails with error
- Status: PASS

TC003: Invalid Email Format
- Input: Malformed email address
- Expected: Validation error
- Status: PASS
```

**Event Booking Test Cases**
```
TC101: Successful Booking
- Input: Valid event and user data
- Expected: Booking confirmed with ticket codes
- Status: PASS

TC102: Insufficient Tickets
- Input: Request more tickets than available
- Expected: Booking fails with error message
- Status: PASS

TC103: Payment Processing
- Input: Valid payment information
- Expected: Payment processed successfully
- Status: PASS
```

**Transport Integration Test Cases**
```
TC201: Combined Booking
- Input: Event ticket + transport booking
- Expected: Both services booked together
- Status: PASS

TC202: Transport Only
- Input: Transport booking without event ticket
- Expected: Transport booked with event ticket option
- Status: PASS

TC203: Seat Availability
- Input: Request seats when few available
- Expected: Real-time availability check
- Status: PASS
```

### E.2 Performance Test Results

**Load Testing Results**
```
Test Configuration:
- Concurrent Users: 50
- Test Duration: 10 minutes
- Test Scenarios: Registration, booking, browsing

Results:
- Average Response Time: 1.2 seconds
- 95th Percentile: 2.1 seconds
- Error Rate: 0.3%
- Throughput: 45 requests/second
```

**Stress Testing Results**
```
Test Configuration:
- Maximum Users: 100
- Ramp-up Time: 5 minutes
- Test Duration: 15 minutes

Results:
- System remained stable up to 75 concurrent users
- Performance degradation started at 80+ users
- No system crashes or data corruption
- Graceful degradation of response times
```

### E.3 Security Test Results

**Vulnerability Assessment**
```
SQL Injection Tests: PASS
- All database queries use prepared statements
- No successful injection attempts

XSS Prevention Tests: PASS
- All user inputs properly sanitized
- No script execution in user content

CSRF Protection Tests: PASS
- Token-based protection implemented
- No successful cross-site request forgery

Authentication Tests: PASS
- Strong password hashing (bcrypt)
- Secure session management
- Proper access control enforcement
```

## Appendix F: Deployment Checklist

### F.1 Pre-Deployment Checklist

**Code Review**
- [ ] All code reviewed and approved
- [ ] No debug code or comments in production
- [ ] Error handling implemented throughout
- [ ] Security measures verified

**Database**
- [ ] Database schema finalized
- [ ] Indexes created for performance
- [ ] Backup procedures tested
- [ ] Data migration scripts prepared

**Security**
- [ ] HTTPS certificate installed
- [ ] Security headers configured
- [ ] File permissions set correctly
- [ ] Sensitive data encrypted

**Performance**
- [ ] Code optimized for production
- [ ] Caching mechanisms enabled
- [ ] Image optimization implemented
- [ ] Database queries optimized

### F.2 Post-Deployment Checklist

**Functionality Testing**
- [ ] User registration and login working
- [ ] Event booking process functional
- [ ] Transport integration working
- [ ] Payment processing operational
- [ ] Notifications being sent

**Monitoring Setup**
- [ ] Error logging configured
- [ ] Performance monitoring active
- [ ] Security monitoring enabled
- [ ] Backup procedures scheduled

**Documentation**
- [ ] User manual updated
- [ ] Admin documentation complete
- [ ] API documentation current
- [ ] Troubleshooting guide available

This completes the comprehensive documentation for the Palestine Tickets system, providing detailed technical specifications, implementation details, testing results, and deployment guidance suitable for a university-level project developed by three computer science students.
