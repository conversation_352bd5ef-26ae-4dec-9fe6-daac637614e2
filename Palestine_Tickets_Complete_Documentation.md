# نظام تذاكر فلسطين - الدوكيومنتيشن الكامل
# Palestine Tickets System - Complete Documentation

---

## الفصل الخامس: تصميم وتطوير النظام
### Chapter 5: System Design and Development

### 5.1 معمارية النظام (System Architecture)

#### 5.1.1 البنية التقنية الفعلية
```
التقنيات المستخدمة:
- اللغة الأساسية: PHP 8.0+
- قاعدة البيانات: MySQL 8.0 (اسم قاعدة البيانات: tickets_db)
- الواجهة الأمامية: HTML5, CSS3, JavaScript, Tailwind CSS
- المكتبات: FontAwesome 6.0, Chart.js
- الخادم: Apache/Nginx مع XAMPP للتطوير
- نمط التصميم: MVC Pattern مع OOP
```

#### 5.1.2 هيكل المجلدات الفعلي
```
new1/ (المجلد الجذر)
├── admin/                  # لوحة الإدارة الشاملة
│   ├── index.php          # لوحة المعلومات الرئيسية
│   ├── events.php         # إدارة الأحداث
│   ├── users.php          # إدارة المستخدمين
│   ├── tickets.php        # إدارة التذاكر
│   ├── sales.php          # تقارير المبيعات
│   ├── login_logs.php     # سجلات الدخول
│   └── payment_cards.php  # إدارة بطاقات الدفع
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   └── img/               # الصور والأيقونات
├── config/                 # إعدادات النظام
│   └── database.php       # إعدادات قاعدة البيانات
├── includes/               # الملفات المشتركة
│   ├── init.php           # تهيئة النظام
│   ├── auth.php           # نظام المصادقة
│   ├── functions.php      # الدوال العامة
│   ├── icons.php          # إدارة الأيقونات
│   ├── notification_functions.php  # دوال الإشعارات
│   ├── admin_functions.php         # دوال الإدارة
│   └── transport_functions.php     # دوال المواصلات
├── lang/                   # ملفات الترجمة (عربي/إنجليزي)
├── transport/              # نظام المواصلات المتكامل
│   ├── index.php          # صفحة المواصلات الرئيسية
│   ├── starting_points.php # اختيار نقطة الانطلاق
│   ├── trips.php          # عرض الرحلات
│   ├── booking.php        # صفحة الحجز
│   ├── payment_method.php # طرق الدفع
│   ├── process_payment.php # معالجة الدفع
│   └── my_bookings.php    # حجوزات المستخدم
├── uploads/                # الملفات المرفوعة
├── logs/                   # ملفات السجلات
└── photo/                  # صور الأحداث
```

### 5.2 قاعدة البيانات الفعلية (Actual Database Design)

#### 5.2.1 جدول المستخدمين (users)
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL,
    user_email VARCHAR(255) UNIQUE NOT NULL,
    user_password VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    profile_image VARCHAR(255) DEFAULT 'default-avatar.png',
    role ENUM('user', 'transport_admin', 'notifications_admin', 'site_admin', 'super_admin') DEFAULT 'user',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 5.2.2 جدول الأحداث (events)
```sql
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    location VARCHAR(255) NOT NULL,
    date_time DATETIME NOT NULL,
    end_time DATETIME,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    capacity INT NOT NULL,
    available_tickets INT NOT NULL,
    category VARCHAR(100),
    organizer VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    image VARCHAR(255),
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    status ENUM('draft', 'published', 'cancelled') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 5.2.3 جدول الطلبات (orders)
```sql
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    order_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    order_code VARCHAR(20) UNIQUE,
    payment_details JSON,
    technical_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

#### 5.2.4 جدول التذاكر (tickets)
```sql
CREATE TABLE tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(50) NOT NULL,
    event_id INT NOT NULL,
    user_id INT NOT NULL,
    ticket_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'used') DEFAULT 'pending',
    qr_code VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 5.2.5 جداول نظام المواصلات

**جدول أنواع المواصلات (transport_types)**
```sql
CREATE TABLE transport_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'bus',
    capacity_min INT DEFAULT 1,
    capacity_max INT DEFAULT 50,
    price_per_km DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**جدول نقاط الانطلاق (starting_points)**
```sql
CREATE TABLE starting_points (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    contact_phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**جدول الرحلات (transport_trips)**
```sql
CREATE TABLE transport_trips (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    starting_point_id INT NOT NULL,
    transport_type_id INT NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME,
    price DECIMAL(10,2) NOT NULL,
    available_seats INT NOT NULL,
    total_seats INT NOT NULL,
    driver_name VARCHAR(255),
    driver_phone VARCHAR(20),
    vehicle_info TEXT,
    route_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id),
    FOREIGN KEY (starting_point_id) REFERENCES starting_points(id),
    FOREIGN KEY (transport_type_id) REFERENCES transport_types(id)
);
```

**جدول حجوزات المواصلات (transport_bookings)**
```sql
CREATE TABLE transport_bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    trip_id INT NOT NULL,
    event_id INT NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    customer_email VARCHAR(255),
    seats_count INT NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    special_notes TEXT,
    payment_method ENUM('bank_transfer', 'cash_on_delivery', 'mobile_pay', 'credit_card') NOT NULL,
    payment_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_details JSON,
    booking_code VARCHAR(20) UNIQUE NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    has_event_ticket ENUM('yes', 'no') DEFAULT 'yes',
    ticket_amount DECIMAL(10,2) DEFAULT 0.00,
    transport_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (trip_id) REFERENCES transport_trips(id),
    FOREIGN KEY (event_id) REFERENCES events(id)
);
```

#### 5.2.6 جداول نظام الإشعارات

**جدول الإشعارات (notifications)**
```sql
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    link VARCHAR(255),
    type ENUM('success','info','warning','danger','booking','payment','admin','transport','event','reminder') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

**جدول إعدادات الإشعارات (notification_settings)**
```sql
CREATE TABLE notification_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL UNIQUE,
    email_enabled BOOLEAN DEFAULT TRUE,
    mobile_enabled BOOLEAN DEFAULT FALSE,
    upcoming_tickets BOOLEAN DEFAULT TRUE,
    event_changes BOOLEAN DEFAULT TRUE,
    transport_updates BOOLEAN DEFAULT TRUE,
    payment_notifications BOOLEAN DEFAULT TRUE,
    admin_announcements BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 5.2.7 جداول نظام الصلاحيات

**جدول صلاحيات الإدارة (admin_permissions)**
```sql
CREATE TABLE admin_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_type ENUM('transport', 'notifications', 'site', 'super') NOT NULL,
    granted_by INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_permission (user_id, permission_type),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (granted_by) REFERENCES users(id)
);
```

**جدول سجلات النشاط (activity_logs)**
```sql
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    description TEXT,
    ip_address VARCHAR(50),
    user_agent TEXT,
    browser VARCHAR(100),
    os VARCHAR(100),
    device VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**جدول سجلات الدخول (login_logs)**
```sql
CREATE TABLE login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    user_agent TEXT NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    device VARCHAR(100) NOT NULL,
    login_time DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

**جدول سجلات التسجيل (registration_logs)**
```sql
CREATE TABLE registration_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(50) NOT NULL,
    user_agent TEXT NOT NULL,
    browser VARCHAR(100) NOT NULL,
    os VARCHAR(100) NOT NULL,
    device VARCHAR(100) NOT NULL,
    registration_time DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 5.3 الصفحات الأساسية للنظام (Core System Pages)

#### 5.3.1 الصفحات الرئيسية
```
الصفحات الأساسية:
├── index.php              # الصفحة الرئيسية - عرض الأحداث المميزة
├── events.php             # عرض جميع الأحداث مع فلترة وبحث
├── event-details.php      # تفاصيل الحدث مع إمكانية الحجز
├── login.php              # تسجيل الدخول مع تتبع محاولات الدخول
├── register.php           # التسجيل مع تسجيل بيانات المستخدم
├── logout.php             # تسجيل الخروج مع تنظيف الجلسة
├── checkout.php           # عملية الدفع الموحدة
├── my-tickets.php         # عرض تذاكر المستخدم
├── notifications.php      # إدارة الإشعارات
├── profile.php            # الملف الشخصي
├── preferences.php        # إعدادات الحساب
├── security.php           # إعدادات الأمان
├── invoices.php           # الفواتير
├── payment-methods.php    # طرق الدفع المحفوظة
├── payment-success.php    # تأكيد نجاح الدفع
├── payment-failed.php     # فشل الدفع
├── forgot-password.php    # استعادة كلمة المرور
└── reset-password.php     # إعادة تعيين كلمة المرور
```

#### 5.3.2 صفحات نظام المواصلات
```
transport/:
├── index.php              # صفحة المواصلات الرئيسية
├── starting_points.php    # اختيار نقطة الانطلاق للحدث
├── trips.php              # عرض الرحلات المتاحة
├── booking.php            # تفاصيل الحجز
├── payment_method.php     # اختيار طريقة الدفع
├── process_payment.php    # معالجة الدفع
├── booking_success.php    # تأكيد نجاح الحجز
├── my_bookings.php        # حجوزات المستخدم
└── setup_transport_types.php  # إعداد أنواع المواصلات
```

#### 5.3.3 صفحات لوحة الإدارة
```
admin/:
├── index.php              # لوحة المعلومات مع الإحصائيات
├── events.php             # إدارة الأحداث (إضافة/تعديل/حذف)
├── users.php              # إدارة المستخدمين والأدوار
├── tickets.php            # إدارة التذاكر وحالاتها
├── sales.php              # تقارير المبيعات والرسوم البيانية
├── login_logs.php         # سجلات الدخول والنشاط
├── payment_cards.php      # إدارة بطاقات الدفع
└── permissions.php        # إدارة الصلاحيات
```

### 5.4 الميزات الوظيفية المطبقة (Implemented Functional Features)

#### 5.4.1 نظام إدارة المستخدمين المتقدم
```php
الميزات المطبقة:
- تسجيل آمن مع تشفير كلمات المرور (password_hash)
- تتبع محاولات الدخول مع تسجيل IP والمتصفح
- نظام أدوار متدرج (5 مستويات)
- إدارة الملفات الشخصية مع رفع الصور
- سجلات شاملة للنشاط
- إعدادات الأمان والخصوصية
```

#### 5.4.2 نظام إدارة الأحداث الشامل
```php
الميزات المطبقة:
- إنشاء أحداث مع تفاصيل كاملة
- نظام الأسعار (سعر أصلي ومخفض)
- إدارة السعة والتذاكر المتاحة
- تصنيفات الأحداث
- حالات الأحداث (مسودة/منشور/ملغي)
- رفع صور الأحداث
- ربط تلقائي بنظام المواصلات
```

#### 5.4.3 نظام الحجز والدفع المتكامل
```php
الميزات المطبقة:
- حجز التذاكر مع التحقق من التوفر
- دفع موحد للتذاكر والمواصلات
- طرق دفع متعددة (بطاقة ائتمان، تحويل بنكي، دفع عند الاستلام)
- تشفير بيانات الدفع
- إنشاء رموز فريدة للتذاكر
- إشعارات فورية للحجز
- فواتير تلقائية
```

#### 5.4.4 نظام المواصلات المتكامل الفريد
```php
الميزات المطبقة:
- ربط تلقائي بالأحداث
- نقاط انطلاق متعددة لكل حدث
- أنواع مواصلات متنوعة (حافلات، سيارات، إلخ)
- جدولة رحلات مرنة
- حجز مقاعد مع عرض المتاح
- دفع مدمج مع التذاكر
- تتبع حالة الحجز
- معلومات السائقين والمركبات
```

#### 5.4.5 نظام الإشعارات الذكي
```php
الميزات المطبقة:
- 10 أنواع مختلفة من الإشعارات
- إشعارات تلقائية للأحداث المهمة
- إعدادات شخصية قابلة للتخصيص
- عرض الإشعارات في الوقت الفعلي
- تعليم الإشعارات كمقروءة
- حذف الإشعارات
- عداد الإشعارات غير المقروءة
- ربط الإشعارات بالصفحات ذات الصلة
```

### 5.5 نظام الأمان المتقدم (Advanced Security System)

#### 5.5.1 أمان البيانات
```php
الإجراءات المطبقة:
- تشفير كلمات المرور باستخدام password_hash()
- استخدام Prepared Statements لمنع SQL Injection
- تنظيف وتطهير جميع المدخلات
- تشفير البيانات الحساسة
- حماية الجلسات من الاختطاف
- رموز CSRF للنماذج المهمة
```

#### 5.5.2 نظام الصلاحيات المتدرج
```php
الأدوار المطبقة:
1. user: مستخدم عادي - حجز وإدارة التذاكر
2. transport_admin: إداري المواصلات - إدارة الرحلات والحجوزات
3. notifications_admin: إداري الإشعارات - إرسال وإدارة الإشعارات
4. site_admin: إداري عام - إدارة الأحداث والمستخدمين
5. super_admin: سوبر إداري - جميع الصلاحيات + إدارة الأدوار
```

#### 5.5.3 سجلات الأمان الشاملة
```php
السجلات المطبقة:
- سجلات الدخول مع IP والمتصفح ونوع الجهاز
- سجلات التسجيل للمستخدمين الجدد
- سجلات النشاط للعمليات المهمة
- سجلات المعاملات المالية
- تتبع تغييرات الصلاحيات
- مراقبة الأنشطة المشبوهة
```

---

*سيتم إكمال باقي الفصول في الملف التالي...*
